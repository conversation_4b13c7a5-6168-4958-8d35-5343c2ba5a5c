<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Service;

use TYPO3\CMS\Core\Domain\Repository\PageRepository;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;

/**
 * Service for handling virtual route page rendering
 * 
 * This service provides a cleaner approach to virtual route handling
 * by working with the existing page resolution system rather than
 * trying to inject template pages directly.
 */
class VirtualRoutePageService
{
    public function __construct(
        private readonly PageRepository $pageRepository
    ) {}

    /**
     * Render virtual route content using template page data
     * 
     * This method works with the existing TSFE page object and
     * overlays template content rather than replacing the page entirely.
     */
    public function renderVirtualRouteContent(
        TypoScriptFrontendController $tsfe,
        array $virtualRouteContext
    ): string {
        $templatePageUid = $virtualRouteContext['landingPage']['template_page_uid'];
        $flightRoute = $virtualRouteContext['flightRoute'];
        
        // Load template page content
        $templateContent = $this->loadTemplatePageContent($templatePageUid);
        
        // Process placeholders in template content
        $processedContent = $this->processPlaceholders($templateContent, $flightRoute);
        
        // Render using TYPO3's content object renderer
        return $this->renderContentWithTypoScript($processedContent, $tsfe);
    }

    /**
     * Load content elements from template page
     */
    protected function loadTemplatePageContent(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        return $queryBuilder
            ->select('*')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0),
                $queryBuilder->expr()->eq('hidden', 0)
            )
            ->orderBy('sorting')
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * Process placeholders in template content
     */
    protected function processPlaceholders(array $templateContent, array $flightRoute): array
    {
        $placeholderService = GeneralUtility::makeInstance(PlaceholderService::class);
        
        foreach ($templateContent as &$contentElement) {
            $contentElement['header'] = $placeholderService->replacePlaceholders(
                $contentElement['header'] ?? '', 
                $flightRoute
            );
            $contentElement['subheader'] = $placeholderService->replacePlaceholders(
                $contentElement['subheader'] ?? '', 
                $flightRoute
            );
            $contentElement['bodytext'] = $placeholderService->replacePlaceholders(
                $contentElement['bodytext'] ?? '', 
                $flightRoute
            );
        }
        
        return $templateContent;
    }

    /**
     * Render content using TYPO3's TypoScript system
     */
    protected function renderContentWithTypoScript(
        array $processedContent, 
        TypoScriptFrontendController $tsfe
    ): string {
        $content = '';
        
        foreach ($processedContent as $contentElement) {
            // Create content object renderer for this element
            $cObj = GeneralUtility::makeInstance(ContentObjectRenderer::class, $tsfe);
            $cObj->start($contentElement, 'tt_content');
            
            // Render using TypoScript configuration
            $cType = $contentElement['CType'];
            if (isset($tsfe->tmpl->setup['tt_content.'][$cType . '.'])) {
                $content .= $cObj->cObjGetSingle(
                    $tsfe->tmpl->setup['tt_content.'][$cType] ?? 'TEXT',
                    $tsfe->tmpl->setup['tt_content.'][$cType . '.'] ?? []
                );
            }
        }
        
        return $content;
    }

    /**
     * Check if current request is a virtual route
     */
    public function isVirtualRoute(): bool
    {
        return isset($GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData']) &&
               ($GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData']['isVirtualRoute'] ?? false);
    }

    /**
     * Get virtual route context data
     */
    public function getVirtualRouteContext(): ?array
    {
        if (!$this->isVirtualRoute()) {
            return null;
        }
        
        return $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData'];
    }
}
