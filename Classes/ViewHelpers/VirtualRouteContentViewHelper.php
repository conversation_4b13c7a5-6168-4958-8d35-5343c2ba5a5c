<?php
namespace Bgs\FlightLandingPages\ViewHelpers;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

/**
 * ViewHelper to render virtual route content by column position
 * 
 * This ViewHelper handles the rendering of template page content for virtual routes,
 * respecting backend layout column positions and applying flight-specific placeholders.
 * 
 * Usage:
 * <flp:virtualRouteContent colPos="0" />
 * <flp:virtualRouteContent colPos="1" />
 */
class VirtualRouteContentViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    protected $escapeOutput = false;

    public function initializeArguments()
    {
        $this->registerArgument('colPos', 'integer', 'Column position to render', false, 0);
        $this->registerArgument('flightRouteData', 'array', 'Flight route data (optional, will be taken from template variables if not provided)', false, []);
    }

    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $colPos = (int)$arguments['colPos'];
        $flightRouteData = $arguments['flightRouteData'];

        error_log("VirtualRouteContentViewHelper: Called for colPos {$colPos}");

        // Get flight route data from template variables if not provided
        if (empty($flightRouteData)) {
            $templateVariableContainer = $renderingContext->getVariableProvider();
            $flightRouteData = $templateVariableContainer->get('flightRouteData') ?? [];
        }

        error_log("VirtualRouteContentViewHelper: Flight route data: " . ($flightRouteData ? 'present' : 'empty'));
        
        // Check if this is a virtual route
        $isVirtualRoute = $flightRouteData['isVirtualRoute'] ?? false;
        error_log("VirtualRouteContentViewHelper: isVirtualRoute = " . ($isVirtualRoute ? 'true' : 'false'));

        if (!$isVirtualRoute) {
            // Not a virtual route, render normal content
            error_log("VirtualRouteContentViewHelper: Rendering normal content for colPos {$colPos}");
            return self::renderNormalContent($colPos, $renderingContext);
        }

        // Virtual route - render template content
        error_log("VirtualRouteContentViewHelper: Rendering virtual route content for colPos {$colPos}");
        return self::renderVirtualRouteContent($colPos, $flightRouteData, $renderingContext);
    }

    /**
     * Render normal page content for non-virtual routes
     */
    protected static function renderNormalContent(int $colPos, RenderingContextInterface $renderingContext): string
    {
        // Get the content object renderer from the rendering context
        $request = $renderingContext->getRequest();
        $cObj = $request->getAttribute('currentContentObject');
        
        if (!$cObj instanceof ContentObjectRenderer) {
            $cObj = GeneralUtility::makeInstance(ContentObjectRenderer::class);
        }

        // Use TYPO3's standard content rendering
        $conf = [
            'table' => 'tt_content',
            'select.' => [
                'orderBy' => 'sorting',
                'where' => 'colPos=' . $colPos,
                'languageField' => 'sys_language_uid',
            ]
        ];
        
        return $cObj->cObjGetSingle('CONTENT', $conf);
    }

    /**
     * Render virtual route content from template page
     */
    protected static function renderVirtualRouteContent(int $colPos, array $flightRouteData, RenderingContextInterface $renderingContext): string
    {
        $templateContent = $flightRouteData['templatePageContent'] ?? [];

        error_log("VirtualRouteContentViewHelper: Template content count: " . count($templateContent));

        if (empty($templateContent)) {
            error_log("VirtualRouteContentViewHelper: No template content found for colPos {$colPos}");
            return '';
        }
        
        // Filter content by column position
        $columnContent = array_filter($templateContent, function($element) use ($colPos) {
            return (int)($element['colPos'] ?? 0) === $colPos;
        });
        
        if (empty($columnContent)) {
            return '';
        }
        
        // Render content elements
        $output = '';
        foreach ($columnContent as $contentElement) {
            $output .= self::renderContentElement($contentElement);
        }
        
        return $output;
    }

    /**
     * Render a single content element
     */
    protected static function renderContentElement(array $contentData): string
    {
        if (empty($contentData)) {
            return '';
        }

        $cType = $contentData['CType'] ?? 'text';
        
        // Basic rendering based on CType
        switch ($cType) {
            case 'text':
            case 'textmedia':
                return self::renderTextElement($contentData);
                
            case 'header':
                return self::renderHeaderElement($contentData);
                
            case 'html':
                return self::renderHtmlElement($contentData);
                
            case 'image':
                return self::renderImageElement($contentData);
                
            default:
                return self::renderGenericElement($contentData);
        }
    }

    /**
     * Render text/textmedia content element
     */
    protected static function renderTextElement(array $data): string
    {
        $output = '';
        
        // Header
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        // Subheader
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        // Bodytext
        if (!empty($data['bodytext'])) {
            // For HTML content, don't escape if it contains HTML tags
            if (strip_tags($data['bodytext']) !== $data['bodytext']) {
                $output .= '<div class="bodytext">' . $data['bodytext'] . '</div>';
            } else {
                $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($data['bodytext'])) . '</div>';
            }
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($data['CType']) . '">' . $output . '</div>';
    }

    /**
     * Render header content element
     */
    protected static function renderHeaderElement(array $data): string
    {
        if (empty($data['header'])) {
            return '';
        }
        
        $headerLevel = (int)($data['header_layout'] ?: 1);
        $output = '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        return '<div class="content-element content-element-header">' . $output . '</div>';
    }

    /**
     * Render HTML content element
     */
    protected static function renderHtmlElement(array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        if (!empty($data['bodytext'])) {
            $output .= $data['bodytext']; // HTML content is not escaped
        }
        
        return '<div class="content-element content-element-html">' . $output . '</div>';
    }

    /**
     * Render image content element
     */
    protected static function renderImageElement(array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        // Basic image rendering - in a real implementation, you'd handle file references
        if (!empty($data['bodytext'])) {
            $output .= '<div class="image-caption">' . htmlspecialchars($data['bodytext']) . '</div>';
        }
        
        return '<div class="content-element content-element-image">' . $output . '</div>';
    }

    /**
     * Render generic content element
     */
    protected static function renderGenericElement(array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        if (!empty($data['bodytext'])) {
            // For HTML content, don't escape if it contains HTML tags
            if (strip_tags($data['bodytext']) !== $data['bodytext']) {
                $output .= '<div class="bodytext">' . $data['bodytext'] . '</div>';
            } else {
                $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($data['bodytext'])) . '</div>';
            }
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($data['CType']) . '">' . $output . '</div>';
    }
}
