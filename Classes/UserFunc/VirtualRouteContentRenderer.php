<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\UserFunc;

use Bgs\FlightLandingPages\Service\PlaceholderService;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Domain\Repository\PageRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;

/**
 * UserFunc to replace lib.dynamicContent for virtual routes
 * 
 * This UserFunc handles both virtual routes and normal pages:
 * - For virtual routes: Renders template page content with placeholder replacement
 * - For normal pages: Renders standard TYPO3 content elements
 */
class VirtualRouteContentRenderer
{
    /**
     * Main render method called by TypoScript USER object
     */
    public function render(string $content, array $conf, ContentObjectRenderer $cObj): string
    {
        error_log("VirtualRouteContentRenderer: UserFunc called");

        // Get virtual route data from GLOBALS (stored by VirtualRouteEnhancementMiddleware)
        $virtualRouteData = $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData'] ?? null;

        error_log("VirtualRouteContentRenderer: Virtual route data in GLOBALS: " . ($virtualRouteData ? 'present' : 'null'));

        if (!$virtualRouteData || !($virtualRouteData['isVirtualRoute'] ?? false)) {
            error_log("VirtualRouteContentRenderer: Not a virtual route, rendering normal content");
            return $this->renderNormalContent($conf, $cObj);
        }

        error_log("VirtualRouteContentRenderer: Rendering virtual route content");

        // This is a virtual route - render template page content
        return $this->renderVirtualRouteContent($virtualRouteData, $conf, $cObj);
    }

    /**
     * Render normal TYPO3 content elements
     */
    protected function renderNormalContent(array $conf, ContentObjectRenderer $cObj): string
    {
        // Use TYPO3's standard CONTENT object to render normal content
        $contentConf = [
            'table' => 'tt_content',
            'select.' => [
                'includeRecordsWithoutDefaultTranslation' => '1',
                'orderBy' => 'sorting',
                'where' => '{#colPos}={register:colPos}',
                'where.' => [
                    'insertData' => '1'
                ],
                'pidInList.' => [
                    'data' => 'register:pageUid',
                    'override.' => [
                        'data' => 'register:contentFromPid'
                    ]
                ]
            ]
        ];

        return $cObj->cObjGetSingle('CONTENT', $contentConf);
    }

    /**
     * Render virtual route content from template page
     */
    protected function renderVirtualRouteContent(array $virtualRouteData, array $conf, ContentObjectRenderer $cObj): string
    {
        $landingPage = $virtualRouteData['landingPage'];
        $flightRoute = $virtualRouteData['flightRoute'];

        // Get current column position from register
        $colPos = (int)($cObj->data['colPos'] ?? 0);

        // Load template page content elements
        $templateContent = $this->loadTemplatePageContent($landingPage['uid'], $colPos);

        if (empty($templateContent)) {
            return '';
        }

        // Process content with placeholder replacement using existing PlaceholderService
        $placeholderService = GeneralUtility::makeInstance(PlaceholderService::class);
        $output = '';

        foreach ($templateContent as $contentElement) {
            $processedElement = $this->processContentElementPlaceholders($contentElement, $flightRoute, $placeholderService);
            $output .= $this->renderContentElement($processedElement, $cObj);
        }

        return $output;
    }

    /**
     * Load content elements from template page for specific column
     */
    protected function loadTemplatePageContent(int $templatePageUid, int $colPos): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('*')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('colPos', $queryBuilder->createNamedParameter($colPos, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->orderBy('sorting')
            ->executeQuery();

        $content = $result->fetchAllAssociative();
        
        // Apply language overlays if needed
        $pageRepository = GeneralUtility::makeInstance(PageRepository::class);
        $language = $GLOBALS['TSFE']->getLanguage() ?? null;
        
        if ($language && $language->getLanguageId() > 0) {
            $content = $pageRepository->getLanguageOverlay('tt_content', $content, $language);
        }
        
        return $content;
    }

    /**
     * Process content element placeholders using the existing PlaceholderService
     */
    protected function processContentElementPlaceholders(array $contentElement, array $flightRoute, PlaceholderService $placeholderService): array
    {
        $processedElement = $contentElement;

        // Fields that may contain placeholders (same as in VirtualRouteFrontendController)
        $fieldsToProcess = ['header', 'subheader', 'bodytext', 'header_link'];
        
        foreach ($fieldsToProcess as $field) {
            if (isset($contentElement[$field]) && !empty($contentElement[$field])) {
                $processedElement[$field] = $placeholderService->replacePlaceholders(
                    $contentElement[$field],
                    $flightRoute
                );
            }
        }

        return $processedElement;
    }

    /**
     * Render a single content element using TYPO3's standard rendering
     */
    protected function renderContentElement(array $contentElement, ContentObjectRenderer $cObj): string
    {
        // Create a new ContentObjectRenderer for this element
        $elementCObj = GeneralUtility::makeInstance(ContentObjectRenderer::class);
        $elementCObj->start($contentElement, 'tt_content');
        
        // Use TYPO3's standard content element rendering
        $cType = $contentElement['CType'] ?? 'text';
        
        // Try to render using TypoScript configuration for this content type
        if (isset($GLOBALS['TSFE']->tmpl->setup['tt_content.'][$cType . '.'])) {
            return $elementCObj->cObjGetSingle(
                $GLOBALS['TSFE']->tmpl->setup['tt_content.'][$cType] ?? 'TEXT',
                $GLOBALS['TSFE']->tmpl->setup['tt_content.'][$cType . '.'] ?? []
            );
        }
        
        // Fallback to basic rendering
        return $this->renderBasicContentElement($contentElement);
    }

    /**
     * Basic content element rendering fallback
     */
    protected function renderBasicContentElement(array $contentElement): string
    {
        $output = '';
        
        // Header
        if (!empty($contentElement['header'])) {
            $headerLevel = (int)($contentElement['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($contentElement['header']) . '</h' . $headerLevel . '>';
        }
        
        // Subheader
        if (!empty($contentElement['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($contentElement['subheader']) . '</p>';
        }
        
        // Bodytext
        if (!empty($contentElement['bodytext'])) {
            // For HTML content, don't escape if it contains HTML tags
            if (strip_tags($contentElement['bodytext']) !== $contentElement['bodytext']) {
                $output .= '<div class="bodytext">' . $contentElement['bodytext'] . '</div>';
            } else {
                $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($contentElement['bodytext'])) . '</div>';
            }
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($contentElement['CType'] ?? 'text') . '">' . $output . '</div>';
    }
}
