<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Middleware;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Routing\SiteRouteResult;
use TYPO3\CMS\Core\Site\Entity\Site;

/**
 * Virtual Route Handler Middleware
 *
 * This middleware runs BEFORE PageResolver to intercept virtual routes
 * and modify the request to point to the template page instead.
 * This prevents PageResolver from rejecting virtual routes with 404
 * and simplifies processing by making the request point directly to the template page.
 */
class VirtualRouteHandler implements MiddlewareInterface
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        error_log("VirtualRouteHandler: *** MIDDLEWARE CALLED ***");

        $site = $request->getAttribute('site');
        if (!$site instanceof Site) {
            error_log("VirtualRouteHandler: No site found, passing through");
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');
        $basePath = rtrim($site->getBase()->getPath(), '/');
        error_log("VirtualRouteHandler: Processing path: {$path}");
        error_log("VirtualRouteHandler: Site base path: {$basePath}");

        // Clear any previous virtual route state
        $this->virtualRouteService->clearVirtualRoute();

        // Detect virtual route
        $virtualRouteMatch = $this->virtualRouteService->detectVirtualRoute($path, $site);

        if ($virtualRouteMatch) {
            error_log("VirtualRouteHandler: Virtual route detected! Modifying request...");

            // Store virtual route context for later processing by PSR-14 events
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);

            // Load template page data to get its slug
            $templatePageUid = $virtualRouteMatch['templatePageUid'];
            $templatePage = $this->virtualRouteService->loadTemplatePage($templatePageUid);

            if (!$templatePage) {
                error_log("VirtualRouteHandler: Template page not found for UID: {$templatePageUid}");
                return $handler->handle($request);
            }

            // Use template page path instead of landing page path
            $templatePagePath = $this->getTemplatePagePath($templatePage, $site);

            error_log("VirtualRouteHandler: Redirecting to template page path: {$templatePagePath}");

            // Create a new URI pointing to the template page
            $newUri = $request->getUri()->withPath($templatePagePath);
            $modifiedRequest = $request->withUri($newUri);

            // Also update the routing attribute to match the new path
            $previousResult = $request->getAttribute('routing');
            if ($previousResult instanceof SiteRouteResult) {
                error_log("VirtualRouteHandler: Updating routing attribute");
                // Create a new SiteRouteResult with the modified tail
                $basePath = rtrim($site->getBase()->getPath(), '/');

                // Only remove base path from the beginning of the template page path
                $newTail = $templatePagePath;
                if (str_starts_with($templatePagePath, $basePath)) {
                    $newTail = substr($templatePagePath, strlen($basePath));
                }
                $newTail = ltrim($newTail, '/');
                error_log("VirtualRouteHandler: New tail: {$newTail}");
                $newRouteResult = new SiteRouteResult(
                    $newUri,
                    $previousResult->getSite(),
                    $previousResult->getLanguage(),
                    $newTail
                );
                $modifiedRequest = $modifiedRequest->withAttribute('routing', $newRouteResult);
            }

            // Add a flag to indicate this is a virtual route request
            $modifiedRequest = $modifiedRequest->withAttribute('virtual_route', $virtualRouteMatch);

            error_log("VirtualRouteHandler: About to pass modified request to next middleware");
            error_log("VirtualRouteHandler: Modified request URI: " . $modifiedRequest->getUri()->getPath());

            // Store the template page UID in the request for early page replacement
            $modifiedRequest = $modifiedRequest->withAttribute('virtual_route_template_page_uid', $templatePageUid);
            error_log("VirtualRouteHandler: Added template page UID to request: {$templatePageUid}");
            $response = $handler->handle($modifiedRequest);

            error_log("VirtualRouteHandler: Response received from downstream middlewares");
            return $response;
        }

        error_log("VirtualRouteHandler: No virtual route match, passing through");
        return $handler->handle($request);
    }

    /**
     * Get the path for the template page that PageResolver can find
     */
    private function getTemplatePagePath(array $templatePage, Site $site): string
    {
        // Use the template page slug
        $slug = $templatePage['slug'] ?? '';

        // Ensure it starts with the site base path
        $basePath = rtrim($site->getBase()->getPath(), '/');

        if (!str_starts_with($slug, $basePath)) {
            $slug = $basePath . '/' . ltrim($slug, '/');
        }

        return $slug;
    }
}
