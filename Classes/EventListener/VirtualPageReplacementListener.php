<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent;

/**
 * Virtual Page Replacement Listener
 * 
 * Listens to AfterPageWithRootLineIsResolvedEvent to replace the resolved page
 * with virtual page data when processing virtual routes.
 */
class VirtualPageReplacementListener
{
    public function __construct(
        private readonly VirtualRouteService $virtualRouteService
    ) {}

    public function __invoke(AfterPageWithRootLineIsResolvedEvent $event): void
    {
        error_log("VirtualPageReplacementListener: *** EVENT LISTENER CALLED! ***");

        // Get virtual route data directly from the request attribute (set by middleware)
        $request = $event->getRequest();
        $virtualRouteMatch = $request->getAttribute('virtual_route');

        if (!$virtualRouteMatch) {
            error_log("VirtualPageReplacementListener: No virtual route context, skipping");
            return;
        }

        error_log("VirtualPageReplacementListener: Processing virtual route - replacing page data");
        error_log("VirtualPageReplacementListener: Virtual route match data: " . print_r($virtualRouteMatch, true));

        if (!isset($virtualRouteMatch['templatePageUid'])) {
            error_log("VirtualPageReplacementListener: templatePageUid not found in virtual route match");
            return;
        }

        error_log("VirtualPageReplacementListener: Template page UID: {$virtualRouteMatch['templatePageUid']}");

        // Get the controller and access page data directly
        $controller = $event->getController();
        $currentPage = $controller->page; // This is now the template page (due to setPageId)
        $currentRootLine = $controller->rootLine;

        error_log("VirtualPageReplacementListener: Current page UID: {$currentPage['uid']} (should be template page)");
        error_log("VirtualPageReplacementListener: Current page title: {$currentPage['title']}");

        //var_dump($virtualRouteMatch);
        // Get landing page data from virtual route match
        $landingPageData = $virtualRouteMatch['landingPage'];
        error_log("VirtualPageReplacementListener: Landing page UID: {$landingPageData['uid']}, Title: {$landingPageData['title']}");
        $routeData = $virtualRouteMatch['flightRoute'];
        error_log("VirtualPageReplacementListener: Route data: {$routeData['origin_code']}, Title: {$routeData['destination_code']}");

        // Create a hybrid page: landing page structure + template page content
        // Start with template page (for TypoScript resolution) but use landing page properties
        $modifiedPage = $currentPage; // Start with template page

        // Replace structural properties with landing page data
        $structuralFields = [
            'uid',      // Use landing page UID for routing
            'pid',      // Use landing page PID for hierarchy
            'slug',     // Use landing page slug for routing
            'sorting',  // Use landing page sorting
            'crdate',   // Use landing page creation date
            //'tstamp',   // Use landing page timestamp
            // Keep other structural fields from landing page
            'deleted',
            'hidden',
            'starttime',
            'endtime',
            'fe_group',
            'sys_language_uid',
            'l10n_parent',
            'l10n_source',
        ];

        foreach ($structuralFields as $field) {
            if (isset($landingPageData[$field])) {
                $modifiedPage[$field] = $landingPageData[$field];
            }
        }

        error_log("VirtualPageReplacementListener: Created hybrid page - UID: {$modifiedPage['uid']}, PID: {$modifiedPage['pid']}");
        error_log("VirtualPageReplacementListener: Hybrid page title: {$modifiedPage['title']}");

        // Update the rootline to include our modified page
        $modifiedRootLine = $currentRootLine;
        if (!empty($modifiedRootLine)) {
            // Replace the last entry (current page) with our modified page
            $modifiedRootLine[array_key_first($modifiedRootLine)] = $modifiedPage;
        }

        // Set the modified page and rootline directly on the controller
        $controller->page = $modifiedPage;
        $controller->rootLine = $modifiedRootLine;

        error_log("VirtualPageReplacementListener: Page replaced with template page data");
        //error_log("VirtualPageReplacementListener: New page title: {$modifiedPage['title']}");
    }
}
