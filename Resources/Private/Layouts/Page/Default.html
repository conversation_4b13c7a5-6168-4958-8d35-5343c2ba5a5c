<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><f:if condition="{data.title}">{data.title}</f:if><f:if condition="{data.seo_title}"> - {data.seo_title}</f:if></title>
    
    <!-- Basic styling for Flight Landing Pages -->
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .flight-landing-page {
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero-section {
            background: #f8f9fa;
            padding: 40px 20px;
            margin-bottom: 30px;
            border-radius: 8px;
        }
        .main-content {
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .footer-content {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .page-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        .sidebar-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        .debug-info {
            font-size: 12px;
            color: #666;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .page-content {
                grid-template-columns: 1fr;
            }
            .hero-section, .main-content, .footer-content, .sidebar-content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="page-wrapper">
        <header class="page-header">
            <h1><f:if condition="{data.title}">{data.title}<f:else>Flight Landing Page</f:else></f:if></h1>
        </header>
        
        <main class="page-main">
            <f:render section="Main" />
        </main>
        
        <footer class="page-footer">
            <p>&copy; 2024 Flight Landing Pages</p>
        </footer>
    </div>
</body>
</html>
