<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><f:if condition="{data.title}">{data.title}</f:if><f:if condition="{data.seo_title}"> - {data.seo_title}</f:if></title>

    <!-- Basic styling for Flight Landing Pages -->
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .flight-landing-page {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }
        .hero-section h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        .main-content {
            padding: 40px;
        }
        .footer-content {
            background: #f8f9fa;
            padding: 40px;
            border-top: 1px solid #dee2e6;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-size: 0.9rem;
            color: #856404;
        }
        @media (max-width: 768px) {
            body { padding: 10px; }
            .hero-section { padding: 40px 20px; }
            .hero-section h1 { font-size: 2rem; }
            .main-content, .footer-content { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="flight-landing-page">
        <!-- Debug information (remove in production) -->
        <f:if condition="{settings.debug}">
            <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
                <h4>Debug Information</h4>
                <p><strong>Page Layout:</strong> {data.backend_layout}</p>
                <p><strong>Page Type:</strong> {data.doktype}</p>
                <p><strong>Is Virtual Route:</strong> {flightRouteData.isVirtualRoute}</p>
                <f:if condition="{flightRouteData.isVirtualRoute}">
                    <p><strong>Flight Route:</strong> {flightRouteData.currentFlightRoute.originCode} → {flightRouteData.currentFlightRoute.destinationCode}</p>
                </f:if>
            </div>
        </f:if>

        <!-- Hero Section (colPos 10) -->
        <div class="hero-section">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="10" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '10'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Main Content (colPos 0) -->
        <div class="main-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="0" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Footer Content (colPos 2) -->
        <div class="footer-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="2" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '2'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Flight Route Data (available for custom integration) -->
        <f:if condition="{flightRouteData.isVirtualRoute}">
            <div class="flight-route-data" style="display: none;">
                <!-- This data is available for JavaScript or other integrations -->
                <script type="application/json" id="flight-route-data">
                    {
                        "isVirtualRoute": true,
                        "originCode": "{flightRouteData.currentFlightRoute.originCode}",
                        "originName": "{flightRouteData.currentFlightRoute.originName}",
                        "originType": "{flightRouteData.currentFlightRoute.originType}",
                        "destinationCode": "{flightRouteData.currentFlightRoute.destinationCode}",
                        "destinationName": "{flightRouteData.currentFlightRoute.destinationName}",
                        "destinationType": "{flightRouteData.currentFlightRoute.destinationType}",
                        "routeSlug": "{flightRouteData.currentFlightRoute.routeSlug}"
                    }
                </script>
            </div>
        </f:if>
    </div>
</body>
</html>
