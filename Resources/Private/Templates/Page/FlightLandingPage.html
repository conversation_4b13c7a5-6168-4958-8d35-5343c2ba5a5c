<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}

<f:layout name="Default" />

<f:section name="Main">
    <div class="flight-landing-page">
        <!-- Debug information (remove in production) -->
        <f:if condition="{settings.debug}">
            <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
                <h4>Debug Information</h4>
                <p><strong>Page Layout:</strong> {data.backend_layout}</p>
                <p><strong>Page Type:</strong> {data.doktype}</p>
                <p><strong>Is Virtual Route:</strong> {flightRouteData.isVirtualRoute}</p>
                <f:if condition="{flightRouteData.isVirtualRoute}">
                    <p><strong>Flight Route:</strong> {flightRouteData.currentFlightRoute.originCode} → {flightRouteData.currentFlightRoute.destinationCode}</p>
                </f:if>
            </div>
        </f:if>

        <!-- Hero Section (colPos 10) -->
        <div class="hero-section">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="10" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '10'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Main Content (colPos 0) -->
        <div class="main-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="0" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Footer Content (colPos 2) -->
        <div class="footer-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="2" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '2'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Flight Route Data (available for custom integration) -->
        <f:if condition="{flightRouteData.isVirtualRoute}">
            <div class="flight-route-data" style="display: none;">
                <!-- This data is available for JavaScript or other integrations -->
                <script type="application/json" id="flight-route-data">
                    {
                        "isVirtualRoute": true,
                        "originCode": "{flightRouteData.currentFlightRoute.originCode}",
                        "originName": "{flightRouteData.currentFlightRoute.originName}",
                        "originType": "{flightRouteData.currentFlightRoute.originType}",
                        "destinationCode": "{flightRouteData.currentFlightRoute.destinationCode}",
                        "destinationName": "{flightRouteData.currentFlightRoute.destinationName}",
                        "destinationType": "{flightRouteData.currentFlightRoute.destinationType}",
                        "routeSlug": "{flightRouteData.currentFlightRoute.routeSlug}"
                    }
                </script>
            </div>
        </f:if>
    </div>
</f:section>
</html>
