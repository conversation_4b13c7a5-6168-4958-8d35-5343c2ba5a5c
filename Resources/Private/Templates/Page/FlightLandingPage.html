<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}
213123121321212313131
<f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
<!-- Hero Section (colPos 10) -->
<f:if condition="{flightRouteData.isVirtualRoute}">
    <f:then>
        <flp:virtualRouteContent colPos="10" />
    </f:then>
    <f:else>
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '10'}" />
    </f:else>
</f:if>

<!-- Main Content (colPos 0) -->
<f:if condition="{flightRouteData.isVirtualRoute}">
    <f:then>
        <flp:virtualRouteContent colPos="0" />
    </f:then>
    <f:else>
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
    </f:else>
</f:if>

<!-- Footer Content (colPos 2) -->
<f:if condition="{flightRouteData.isVirtualRoute}">
    <f:then>
        <flp:virtualRouteContent colPos="2" />
    </f:then>
    <f:else>
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '2'}" />
    </f:else>
</f:if>
