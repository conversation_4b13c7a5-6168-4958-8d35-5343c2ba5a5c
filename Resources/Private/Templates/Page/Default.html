<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}

<!-- Main Content (colPos 0) -->
<f:if condition="{flightRouteData.isVirtualRoute}">
    <f:then>
        <flp:virtualRouteContent colPos="0" />
    </f:then>
    <f:else>
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
    </f:else>
</f:if>

<!-- Sidebar Content (colPos 1) -->
<f:if condition="{flightRouteData.isVirtualRoute}">
    <f:then>
        <flp:virtualRouteContent colPos="1" />
    </f:then>
    <f:else>
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '1'}" />
    </f:else>
</f:if>
