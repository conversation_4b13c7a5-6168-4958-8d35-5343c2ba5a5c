<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><f:if condition="{data.title}">{data.title}<f:else>Page</f:else></f:if></title>

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .page-content {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            overflow: hidden;
        }
        .main-content {
            padding: 40px;
        }
        .sidebar-content {
            background: #f8f9fa;
            padding: 40px;
            border-left: 1px solid #dee2e6;
        }
        @media (max-width: 768px) {
            .page-content {
                grid-template-columns: 1fr;
            }
            .main-content, .sidebar-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="page-content">
        <!-- Main Content (colPos 0) -->
        <div class="main-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="0" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Sidebar Content (colPos 1) if available -->
        <div class="sidebar-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="1" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '1'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Flight Route Data (available for custom integration) -->
        <f:if condition="{flightRouteData.isVirtualRoute}">
            <div class="flight-route-data" style="display: none;">
                <!-- This data is available for JavaScript or other integrations -->
                <script type="application/json" id="flight-route-data">
                    {
                        "isVirtualRoute": true,
                        "originCode": "{flightRouteData.currentFlightRoute.originCode}",
                        "originName": "{flightRouteData.currentFlightRoute.originName}",
                        "originType": "{flightRouteData.currentFlightRoute.originType}",
                        "destinationCode": "{flightRouteData.currentFlightRoute.destinationCode}",
                        "destinationName": "{flightRouteData.currentFlightRoute.destinationName}",
                        "destinationType": "{flightRouteData.currentFlightRoute.destinationType}",
                        "routeSlug": "{flightRouteData.currentFlightRoute.routeSlug}"
                    }
                </script>
            </div>
        </f:if>
    </div>
</body>
</html>
