<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

{namespace flp=Bgs\FlightLandingPages\ViewHelpers}

<f:layout name="Default" />

<f:section name="Main">
    <div class="page-content">
        <!-- Main Content (colPos 0) -->
        <div class="main-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="0" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Sidebar Content (colPos 1) if available -->
        <div class="sidebar-content">
            <f:if condition="{flightRouteData.isVirtualRoute}">
                <f:then>
                    <!-- Virtual route: render template content with flight data -->
                    <flp:virtualRouteContent colPos="1" />
                </f:then>
                <f:else>
                    <!-- Normal page: render standard content -->
                    <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '1'}" />
                </f:else>
            </f:if>
        </div>

        <!-- Flight Route Data (available for custom integration) -->
        <f:if condition="{flightRouteData.isVirtualRoute}">
            <div class="flight-route-data" style="display: none;">
                <!-- This data is available for JavaScript or other integrations -->
                <script type="application/json" id="flight-route-data">
                    {
                        "isVirtualRoute": true,
                        "originCode": "{flightRouteData.currentFlightRoute.originCode}",
                        "originName": "{flightRouteData.currentFlightRoute.originName}",
                        "originType": "{flightRouteData.currentFlightRoute.originType}",
                        "destinationCode": "{flightRouteData.currentFlightRoute.destinationCode}",
                        "destinationName": "{flightRouteData.currentFlightRoute.destinationName}",
                        "destinationType": "{flightRouteData.currentFlightRoute.destinationType}",
                        "routeSlug": "{flightRouteData.currentFlightRoute.routeSlug}"
                    }
                </script>
            </div>
        </f:if>
    </div>
</f:section>
</html>
