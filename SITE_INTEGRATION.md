# Site Integration Guide for Flight Landing Pages

This guide explains how Flight Landing Pages render as **normal TYPO3 pages** using your existing site templates and layouts.

## Overview

Flight Landing Pages (doktype 201) render exactly like standard TYPO3 pages with these key features:

- ✅ **Uses your site's existing PAGE object configuration**
- ✅ **Renders with your site's templates, layouts, and partials**
- ✅ **Maintains your site's navigation, styling, and functionality**
- ✅ **Adds flight-specific data via `{flightRouteData}` template variable**
- ✅ **No custom rendering or template manipulation**

## How It Works

### 1. Standard Page Rendering

Flight Landing Pages render as normal TYPO3 pages using your site's:
- PAGE object configuration
- Template files
- Layout files
- Partial files
- TypoScript setup
- CSS and JavaScript

### 2. Flight Data Integration

The extension adds a `FlightRouteProcessor` data processor to Flight Landing Pages (doktype 201) that:

- Detects virtual routes from URLs (e.g., `/flights/ber-sof`)
- Loads flight route data for the current page
- Processes template page content with placeholders
- Makes flight data available to your templates via `{flightRouteData}`

### 2. Template Integration Options

You have several options to display flight content in your existing templates:

#### Option A: Using the Flight Content Partial

Include the provided partial in your site template:

```html
<!-- In your site's page template -->
<f:render partial="FlightContent" arguments="{_all}" />
```

This will automatically show flight-specific content when viewing virtual routes.

#### Option B: Custom Integration

Add flight-specific sections directly to your templates:

```html
<!-- In your site's page template -->
<f:if condition="{flightRouteData.isVirtualRoute}">
    <div class="flight-header">
        <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
        <p class="route-info">
            {flightRouteData.currentFlightRoute.originCode} → {flightRouteData.currentFlightRoute.destinationCode}
        </p>
    </div>
</f:if>

<!-- Your normal page content -->
<f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
```

#### Option C: Column-Based Virtual Route Integration (Recommended)

Use the VirtualRouteContentViewHelper for proper backend layout support:

```html
<!-- Add namespace -->
<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

<!-- In your template -->
<f:section name="Main">
    <div class="container">
        <!-- Main content column (respects virtual routes) -->
        <div class="main-content">
            <flp:virtualRouteContent colPos="0" />
        </div>

        <!-- Sidebar content (respects virtual routes) -->
        <div class="sidebar">
            <flp:virtualRouteContent colPos="1" />
        </div>
    </div>
</f:section>
```

This approach:
- ✅ **Automatically handles virtual routes** - shows template content for virtual routes, normal content otherwise
- ✅ **Respects backend layouts** - uses the configured backend layout with proper column positioning
- ✅ **Processes placeholders** - flight-specific data is automatically replaced in template content
- ✅ **Maintains TYPO3 functionality** - all normal page features work as expected

#### Option D: Legacy Partial Integration

Use the FlightContentViewHelper for backward compatibility:

```html
<flp:flightContent>
    <f:if condition="{flightRouteData.isVirtualRoute}">
        <!-- Flight-specific content -->
        <div class="flight-page">
            <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
            <!-- Template content with processed placeholders -->
            <f:for each="{flightRouteData.templatePageContent}" as="contentElement">
                <flp:contentElement data="{contentElement}" />
            </f:for>
        </div>
    </f:if>
</flp:flightContent>
```

## Virtual Route Rendering

For comprehensive virtual route functionality, including backend layout priority logic and advanced integration options, see the dedicated **[Virtual Route Integration Guide](VIRTUAL_ROUTE_INTEGRATION.md)**.

## Implementation Steps

### 1. Configure Backend Layout

Flight Landing Pages (doktype 201) now include the **Appearance** tab with backend layout configuration:

1. Edit your Flight Landing Page
2. Go to the **Appearance** tab
3. Select your desired **Backend Layout**
4. Configure **Backend Layout (Subpages)** if needed

This allows you to:
- Use your site's existing backend layouts
- Configure content columns properly
- Maintain consistent content structure

### 2. Update Your Site Template

Add the flight content partial to your main page template where you want flight-specific content to appear:

```html
<!-- Example: In your site's Resources/Private/Templates/Page/Default.html -->
<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:layout name="Default" />

<f:section name="Main">
    <div class="container">
        <header>
            <!-- Your site navigation -->
            <f:render partial="Navigation" arguments="{_all}" />
        </header>

        <main>
            <!-- Flight content (only shows on virtual routes) -->
            <f:render partial="FlightContent" arguments="{_all}" />

            <!-- Normal page content -->
            <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
        </main>

        <footer>
            <!-- Your site footer -->
            <f:render partial="Footer" arguments="{_all}" />
        </footer>
    </div>
</f:section>
</html>
```

### 3. Add ViewHelper Namespace (Optional)

If you want to use the FlightContentViewHelper, add the namespace to your template:

```html
<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Vendor/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">
```

### 4. Copy the Flight Content Partial

Copy the flight content partial to your site's partials directory:

```bash
# Copy from extension to your site
cp packages/flight_landing_pages/Resources/Private/Partials/FlightContent.html \
   path/to/your/site/Resources/Private/Partials/
```

Then customize it to match your site's styling and structure.

### 5. Style Flight Content

Add CSS for flight-specific elements to your site's stylesheet:

```css
/* Flight Landing Pages Styles */
.flight-route-header {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
}

.route-info {
    font-size: 1.1rem;
    color: #6c757d;
}

.route-info .origin,
.route-info .destination {
    font-weight: bold;
    color: #495057;
}

.flight-template-content {
    margin: 2rem 0;
}

.flight-info {
    background: #e9ecef;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 2rem;
}
```

## Available Template Variables

When viewing a Flight Landing Page, the following variables are available in your templates:

### `{flightRouteData}`

Main flight data object containing:

- `isVirtualRoute` (boolean) - True if viewing a virtual route (e.g., `/flights/ber-sof`)
- `currentFlightRoute` (array) - Current flight route data
- `allFlightRoutes` (array) - All flight routes for this landing page
- `templatePageContent` (array) - Processed template page content elements
- `flightData` (array) - Flight information for placeholder replacement
- `routeData` (array) - Extracted route parameters from URL

### `{flightRouteData.currentFlightRoute}`

Current flight route information:

- `originCode` - Origin airport code (e.g., "BER")
- `originName` - Origin airport/city name (e.g., "Berlin")
- `originType` - Origin type ("airport", "city", "country")
- `destinationCode` - Destination airport code (e.g., "SOF")
- `destinationName` - Destination airport/city name (e.g., "Sofia")
- `destinationType` - Destination type ("airport", "city", "country")
- `routeSlug` - Route slug (e.g., "ber-sof")

### `{flightRouteData.flightData}`

Flight information for placeholders:

- `origin` - Origin name
- `destination` - Destination name
- `price` - Flight price
- `currency` - Price currency
- `airline` - Airline name
- `flight.duration` - Flight duration
- `flight.number` - Flight number

## Testing Integration

### 1. Test Normal Page Access

Visit your Flight Landing Page directly (e.g., `https://yoursite.com/flights`):

- ✅ Should use your site's normal template and styling
- ✅ Should show normal page content
- ✅ Should not show any flight-specific content
- ✅ Navigation and footer should work normally

### 2. Test Virtual Route Access

Visit a virtual route (e.g., `https://yoursite.com/flights/ber-sof`):

- ✅ Should use your site's template and styling
- ✅ Should show flight-specific content in addition to normal content
- ✅ Should display dynamic title and route information
- ✅ Navigation and footer should still work

### 3. Test Template Processing

If you have template pages with placeholders:

- ✅ Placeholders should be replaced with actual flight data
- ✅ Content should maintain your site's styling
- ✅ No raw placeholder text should be visible

## Troubleshooting

### Flight Content Not Showing

1. Check that you've added the flight content partial to your template
2. Verify that the FlightRouteProcessor is working (check `{flightRouteData}` in template)
3. Ensure flight routes are active and properly configured

### Site Styling Broken

1. Verify that the extension is not overriding your PAGE object
2. Check that your site's TypoScript is loading correctly
3. Ensure CSS conflicts are resolved

### Virtual Routes Not Working

1. Check that flight routes are created and active
2. Verify the route slugs match the URL pattern
3. Ensure the FlightRouteProcessor is properly configured

## Best Practices

1. **Keep Your Site's Configuration**: Don't modify your existing TypoScript PAGE object
2. **Use Conditional Rendering**: Only show flight content when `{flightRouteData.isVirtualRoute}` is true
3. **Maintain Consistency**: Style flight content to match your site's design
4. **Test Thoroughly**: Test both normal pages and virtual routes
5. **Graceful Fallback**: Ensure pages work even if flight data is unavailable

## Migration from Previous Versions

If you were using a previous version that overrode your site configuration:

1. Remove any custom PAGE object configurations from the extension
2. Update your site templates to include flight content conditionally
3. Test that your site's normal functionality is restored
4. Verify that flight functionality still works on virtual routes
