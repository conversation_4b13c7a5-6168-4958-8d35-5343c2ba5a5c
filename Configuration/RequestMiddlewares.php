<?php
return [
    'frontend' => [
        // Virtual Route Handler - Must run BEFORE PageResolver to catch virtual routes
        'flight-landing-pages/virtual-route-handler' => [
            'target' => \Bgs\FlightLandingPages\Middleware\VirtualRouteHandler::class,
            'after' => [
                'typo3/cms-frontend/site',
                'typo3/cms-frontend/authentication',
                'typo3/cms-frontend/backend-user-authentication',
            ],
            'before' => [
                'typo3/cms-frontend/page-resolver',
            ],
        ],
    ],
];
